package com.yelink.dfscommon.api.ams;

import com.yelink.dfscommon.annotation.ExceptionCatch;
import com.yelink.dfscommon.pojo.ResponseData;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;


/**
 * <AUTHOR>
 * @description: 生产订单
 * @time 2023/10/22
 */
@FeignClient(value = "ams", path = "/ams")
public interface ProductOrderInterface {

    @ExceptionCatch
    @GetMapping("/v1/open/product_orders/select/{orderId}")
    ResponseData getOrderDetail(@PathVariable Integer orderId);
}
