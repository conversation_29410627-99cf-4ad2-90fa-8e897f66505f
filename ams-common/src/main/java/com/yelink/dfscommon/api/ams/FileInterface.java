package com.yelink.dfscommon.api.ams;

import com.yelink.dfscommon.pojo.ResponseData;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;


/**
 * 通用文件
 * <AUTHOR>
 */
@FeignClient(value = "ams", path = "/ams")
public interface FileInterface {

    /**
     * 销售订单导入
     * @param file 文件
     */
    @PostMapping(value = "/inner/saleOrder/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    void saleOrderImportData(@RequestPart("file") MultipartFile file);
    /**
     * 销售订单进度
     * @return double值
     */
    @PostMapping(value = "/inner/saleOrder/importProgress")
    ResponseData saleOrderImportProgress();


    /**
     * 生产订单导入
     * @param file 文件
     */
    @PostMapping(value = "/inner/productOrder/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    void productOrderImportData(@RequestPart("file") MultipartFile file);
    /**
     * 生产订单导入进度
     * @return double值
     */
    @PostMapping(value = "/inner/productOrder/importProgress")
    ResponseData productOrderImportProgress();


}

