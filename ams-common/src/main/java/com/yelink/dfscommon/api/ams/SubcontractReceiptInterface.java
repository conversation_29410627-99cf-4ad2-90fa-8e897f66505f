package com.yelink.dfscommon.api.ams;

import com.yelink.dfscommon.annotation.ExceptionCatch;
import com.yelink.dfscommon.pojo.ResponseData;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;


/**
 * <AUTHOR>
 * @description: 调用ams对内来料检验接口
 * @time 2021/7/9 11:35
 */
@FeignClient(value = "ams", path = "/ams")
public interface SubcontractReceiptInterface {

    /**
     * 获取收货单详情-根据收货单号
     *
     * @param receiptNumber 物料编号
     * @return List<MaterialRelateDataDTO>
     */
    @ExceptionCatch
    @RequestMapping("/get/receiptNumber/{receiptNumber}")
    ResponseData getEntityByNumber(@RequestParam(value = "receiptNumber") String receiptNumber);

    @ExceptionCatch
    @RequestMapping("/get/receiptNumber")
    ResponseData getEntityByNumber2(@RequestParam String receiptNumber);
}
