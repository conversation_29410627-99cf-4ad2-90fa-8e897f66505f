package com.yelink.dfscommon.api.ams;

import com.yelink.dfscommon.api.dfs.dto.ReceiptProjectContractDTO;
import com.yelink.dfscommon.pojo.ResponseData;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


/**
 * @Description: 调用dfs对内单据接口
 * @Author: wuzhipan
 * @Date: 2023/11/9
 */
@FeignClient(value = "ams", path = "/ams")
public interface AmsReceiptProjectContractInterface {

    /**
     * 通过id获取工作中心详情
     *
     * @param params
     * @return
     */
    @PostMapping("/v1/open/ams/receipt/project/contract/list")
    ResponseData list(@RequestBody ReceiptProjectContractDTO params);
}
