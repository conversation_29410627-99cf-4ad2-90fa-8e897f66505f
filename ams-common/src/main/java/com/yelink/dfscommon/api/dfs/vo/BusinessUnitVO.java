package com.yelink.dfscommon.api.dfs.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yelink.dfscommon.api.dfs.constant.BusinessUnitStateEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class BusinessUnitVO {


    /**
     * id
     */
    private Integer id;

    /**
     * 编码
     */
    private String code;

    /**
     * 名称
     */
    private String name;


    /**
     * 状态
     */
    private BusinessUnitStateEnum state;
    public String getStateName() {
        return Optional.ofNullable(state).map(BusinessUnitStateEnum::getName).orElse(null);
    }

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;
    private String createByName;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    private String updateByName;

}
